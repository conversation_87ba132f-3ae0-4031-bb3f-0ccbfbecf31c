import {
  collection,
  query,
  where,
  getDocs,
  Timestamp,
  doc,
  getDoc
} from 'firebase/firestore';

// Use a conditional import to avoid TypeScript compilation issues
let db: any;
try {
  // This will work in runtime but avoid compilation issues
  db = require('../config/firebase').db;
} catch {
  // Fallback for compilation
  db = {} as any;
}

export interface AnalyticsMetrics {
  totalPrompts: number;
  totalExecutions: number;
  totalDocuments: number;
  avgExecutionTime: number;
  successRate: number;
  totalCost: number;
  avgCostPerExecution: number;
}

export interface ModelUsage {
  name: string;
  usage: number;
  cost: number;
  avgResponseTime: number;
}

export interface ActivityData {
  date: string;
  prompts: number;
  executions: number;
  documents: number;
  cost: number;
}

export interface PerformanceMetrics {
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
    min: number;
    max: number;
  };
  errorRate: number;
  throughput: number;
  uptime: number;
  searchLatency: {
    semantic: number;
    keyword: number;
    hybrid: number;
  };
  embeddingLatency: number;
  cacheHitRate: number;
}

// Phase 3 Advanced Analytics Interfaces
export interface ABTestVariant {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
  weight: number;
}

export interface ABTestConfig {
  id: string;
  name: string;
  description: string;
  variants: ABTestVariant[];
  trafficSplit: number[];
  startDate: Date;
  endDate?: Date;
  status: 'draft' | 'running' | 'paused' | 'completed';
  metrics: string[];
  targetMetric: string;
  minimumSampleSize: number;
  confidenceLevel: number;
}

export interface ABTestResult {
  testId: string;
  variantId: string;
  metric: string;
  value: number;
  sampleSize: number;
  conversionRate?: number;
  confidenceInterval?: [number, number];
  statisticalSignificance?: number;
}

export interface RealTimeMetrics {
  timestamp: Date;
  activeUsers: number;
  requestsPerSecond: number;
  avgResponseTime: number;
  errorRate: number;
  searchQueries: number;
  hybridSearchUsage: number;
  semanticSearchUsage: number;
  keywordSearchUsage: number;
  cacheHitRate: number;
  systemLoad: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

export interface ModelCostBreakdown {
  modelName: string;
  requests: number;
  totalCost: number;
  avgCostPerRequest: number;
  percentage: number;
}

export interface CostTrendData {
  date: string;
  cost: number;
  requests: number;
  efficiency: number;
}

export interface OptimizationSuggestion {
  type: 'model_switch' | 'caching' | 'batching' | 'rate_limiting';
  title: string;
  description: string;
  potentialSavings: number;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
}

export interface CostOptimizationMetrics {
  totalCost: number;
  costPerRequest: number;
  costByModel: ModelCostBreakdown[];
  costTrends: CostTrendData[];
  optimizationSuggestions: OptimizationSuggestion[];
  budgetUtilization: number;
  projectedMonthlyCost: number;
}

export interface UsageAnalytics {
  metrics: AnalyticsMetrics;
  modelUsage: ModelUsage[];
  activityData: ActivityData[];
  performanceMetrics: PerformanceMetrics;
  topPrompts: Array<{
    id: string;
    title: string;
    executions: number;
    successRate: number;
  }>;
  costBreakdown: Array<{
    category: string;
    amount: number;
    percentage: number;
  }>;
}

export type TimeRange = '24h' | '7d' | '30d' | '90d' | '1y';

export class AnalyticsService {
  private executionsCollection = collection(db, 'executions');
  private promptsCollection = collection(db, 'prompts');
  private documentsCollection = collection(db, 'documents');

  // Get comprehensive analytics for a user
  async getUserAnalytics(userId: string, timeRange: TimeRange = '7d'): Promise<UsageAnalytics> {
    try {
      console.log('AnalyticsService: Getting analytics for user:', userId, 'timeRange:', timeRange);
      const timeRangeMs = this.getTimeRangeMs(timeRange);
      const startDate = Timestamp.fromDate(new Date(Date.now() - timeRangeMs));
      console.log('AnalyticsService: Start date:', startDate.toDate());

      // Fetch all data in parallel
      console.log('AnalyticsService: Fetching data in parallel...');
      const [
        metrics,
        modelUsage,
        activityData,
        performanceMetrics,
        topPrompts,
        costBreakdown
      ] = await Promise.all([
        this.getMetrics(userId, startDate),
        this.getModelUsage(userId, startDate),
        this.getActivityData(userId, startDate, timeRange),
        this.getPerformanceMetrics(userId, startDate),
        this.getTopPrompts(userId, startDate),
        this.getCostBreakdown(userId, startDate)
      ]);

      console.log('AnalyticsService: All data fetched successfully');
      return {
        metrics,
        modelUsage,
        activityData,
        performanceMetrics,
        topPrompts,
        costBreakdown
      };
    } catch (error) {
      console.error('AnalyticsService: Error fetching analytics:', error);
      // Return empty data structure instead of throwing to prevent page crash
      return {
        metrics: {
          totalPrompts: 0,
          totalExecutions: 0,
          totalDocuments: 0,
          avgExecutionTime: 0,
          successRate: 0,
          totalCost: 0,
          avgCostPerExecution: 0
        },
        modelUsage: [],
        activityData: [],
        performanceMetrics: {
          responseTime: { avg: 0, p95: 0, p99: 0, min: 0, max: 0 },
          errorRate: 0,
          throughput: 0,
          uptime: 100,
          searchLatency: { semantic: 0, keyword: 0, hybrid: 0 },
          embeddingLatency: 0,
          cacheHitRate: 0
        },
        topPrompts: [],
        costBreakdown: []
      };
    }
  }

  // Get workspace analytics
  async getWorkspaceAnalytics(workspaceId: string, timeRange: TimeRange = '7d'): Promise<UsageAnalytics> {
    try {
      const timeRangeMs = this.getTimeRangeMs(timeRange);
      const startDate = Timestamp.fromDate(new Date(Date.now() - timeRangeMs));

      // Similar to user analytics but filtered by workspace
      const [
        metrics,
        modelUsage,
        activityData,
        performanceMetrics,
        topPrompts,
        costBreakdown
      ] = await Promise.all([
        this.getWorkspaceMetrics(workspaceId, startDate),
        this.getWorkspaceModelUsage(workspaceId, startDate),
        this.getWorkspaceActivityData(workspaceId, startDate, timeRange),
        this.getWorkspacePerformanceMetrics(workspaceId, startDate),
        this.getWorkspaceTopPrompts(workspaceId, startDate),
        this.getWorkspaceCostBreakdown(workspaceId, startDate)
      ]);

      return {
        metrics,
        modelUsage,
        activityData,
        performanceMetrics,
        topPrompts,
        costBreakdown
      };
    } catch (error) {
      console.error('Error fetching workspace analytics:', error);
      throw new Error('Failed to fetch workspace analytics data');
    }
  }

  // Private helper methods
  private async getMetrics(userId: string, startDate: Timestamp): Promise<AnalyticsMetrics> {
    try {
      const executionsQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', startDate)
      );

      const promptsQuery = query(
        this.promptsCollection,
        where('userId', '==', userId)
      );

      const documentsQuery = query(
        this.documentsCollection,
        where('userId', '==', userId)
      );

      const [executionsSnapshot, promptsSnapshot, documentsSnapshot] = await Promise.all([
        getDocs(executionsQuery).catch(() => ({ docs: [], size: 0 })),
        getDocs(promptsQuery).catch(() => ({ docs: [], size: 0 })),
        getDocs(documentsQuery).catch(() => ({ docs: [], size: 0 }))
      ]);

      const executions = executionsSnapshot.docs.map(doc => doc.data());
      const successfulExecutions = executions.filter(exec => exec.status === 'success');

      const totalExecutionTime = executions.reduce((sum, exec) => sum + (exec.executionTime || 0), 0);
      const totalCost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);

      return {
        totalPrompts: promptsSnapshot.size || 0,
        totalExecutions: executionsSnapshot.size || 0,
        totalDocuments: documentsSnapshot.size || 0,
        avgExecutionTime: executions.length > 0 ? totalExecutionTime / executions.length : 0,
        successRate: executions.length > 0 ? (successfulExecutions.length / executions.length) * 100 : 100,
        totalCost,
        avgCostPerExecution: executions.length > 0 ? totalCost / executions.length : 0
      };
    } catch (error) {
      console.warn('Failed to fetch metrics, returning default values:', error);
      return {
        totalPrompts: 0,
        totalExecutions: 0,
        totalDocuments: 0,
        avgExecutionTime: 0,
        successRate: 100,
        totalCost: 0,
        avgCostPerExecution: 0
      };
    }
  }

  private async getModelUsage(userId: string, startDate: Timestamp): Promise<ModelUsage[]> {
    try {
      const executionsQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', startDate)
      );

      const snapshot = await getDocs(executionsQuery).catch(() => ({ docs: [] }));
      const executions = snapshot.docs.map(doc => doc.data());

      // Group by model
      const modelStats: Record<string, {
        usage: number;
        cost: number;
        totalResponseTime: number;
      }> = {};

      executions.forEach(exec => {
        const model = exec.model || 'unknown';
        if (!modelStats[model]) {
          modelStats[model] = { usage: 0, cost: 0, totalResponseTime: 0 };
        }
        modelStats[model].usage++;
        modelStats[model].cost += exec.cost || 0;
        modelStats[model].totalResponseTime += exec.executionTime || 0;
      });

      return Object.entries(modelStats).map(([name, stats]) => ({
        name,
        usage: stats.usage,
        cost: stats.cost,
        avgResponseTime: stats.usage > 0 ? stats.totalResponseTime / stats.usage : 0
      })).sort((a, b) => b.usage - a.usage);
    } catch (error) {
      console.warn('Failed to fetch model usage, returning empty array:', error);
      return [];
    }
  }

  private async getActivityData(userId: string, _startDate: Timestamp, timeRange: TimeRange): Promise<ActivityData[]> {
    try {
      const days = this.getTimeRangeDays(timeRange);
      const activityData: ActivityData[] = [];

      // Generate data for each day in the range
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

        const dayQuery = query(
          this.executionsCollection,
          where('userId', '==', userId),
          where('createdAt', '>=', Timestamp.fromDate(dayStart)),
          where('createdAt', '<', Timestamp.fromDate(dayEnd))
        );

        const snapshot = await getDocs(dayQuery).catch(() => ({ docs: [] }));
        const executions = snapshot.docs.map(doc => doc.data());

        activityData.push({
          date: dayStart.toISOString().split('T')[0],
          prompts: 0, // Would need separate query for prompts created that day
          executions: executions.length,
          documents: 0, // Would need separate query for documents uploaded that day
          cost: executions.reduce((sum, exec) => sum + (exec.cost || 0), 0)
        });
      }

      return activityData;
    } catch (error) {
      console.warn('Failed to fetch activity data, returning empty array:', error);
      return [];
    }
  }

  private async getPerformanceMetrics(userId: string, startDate: Timestamp): Promise<PerformanceMetrics> {
    try {
      const executionsQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', startDate)
      );

      const snapshot = await getDocs(executionsQuery).catch(() => ({ docs: [] }));
      const executions = snapshot.docs.map(doc => doc.data());

      if (executions.length === 0) {
        return {
          responseTime: { avg: 0, p95: 0, p99: 0, min: 0, max: 0 },
          errorRate: 0,
          throughput: 0,
          uptime: 100,
          searchLatency: { semantic: 0, keyword: 0, hybrid: 0 },
          embeddingLatency: 0,
          cacheHitRate: 0
        };
      }

      const responseTimes = executions.map(exec => exec.executionTime || 0).sort((a, b) => a - b);
      const errors = executions.filter(exec => exec.status === 'error').length;

      return {
        responseTime: {
          avg: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
          p95: responseTimes[Math.floor(responseTimes.length * 0.95)] || 0,
          p99: responseTimes[Math.floor(responseTimes.length * 0.99)] || 0,
          min: Math.min(...responseTimes) || 0,
          max: Math.max(...responseTimes) || 0
        },
        errorRate: (errors / executions.length) * 100,
        throughput: executions.length / this.getTimeRangeDays('7d'), // executions per day
        uptime: 99.9, // Would be calculated from system monitoring
        searchLatency: { semantic: 0, keyword: 0, hybrid: 0 },
        embeddingLatency: 0,
        cacheHitRate: 0
      };
    } catch (error) {
      console.warn('Failed to fetch performance metrics, returning default values:', error);
      return {
        responseTime: { avg: 0, p95: 0, p99: 0, min: 0, max: 0 },
        errorRate: 0,
        throughput: 0,
        uptime: 100,
        searchLatency: { semantic: 0, keyword: 0, hybrid: 0 },
        embeddingLatency: 0,
        cacheHitRate: 0
      };
    }
  }

  private async getTopPrompts(userId: string, startDate: Timestamp): Promise<Array<{
    id: string;
    title: string;
    executions: number;
    successRate: number;
  }>> {
    try {
      const executionsQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', startDate)
      );

      const snapshot = await getDocs(executionsQuery).catch(() => ({ docs: [] }));
      const executions = snapshot.docs.map(doc => doc.data());

      // Group by prompt ID
      const promptStats: Record<string, {
        executions: number;
        successes: number;
        title?: string;
      }> = {};

      executions.forEach(exec => {
        const promptId = exec.promptId || 'unknown';
        if (!promptStats[promptId]) {
          promptStats[promptId] = { executions: 0, successes: 0 };
        }
        promptStats[promptId].executions++;
        if (exec.status === 'success') {
          promptStats[promptId].successes++;
        }
      });

      // Get prompt titles
      const topPromptIds = Object.keys(promptStats)
        .sort((a, b) => promptStats[b].executions - promptStats[a].executions)
        .slice(0, 10);

      const promptTitles = await Promise.all(
        topPromptIds.map(async (promptId) => {
          try {
            const promptDoc = await getDoc(doc(this.promptsCollection, promptId));
            return { id: promptId, title: promptDoc.data()?.title || 'Unknown Prompt' };
          } catch {
            return { id: promptId, title: 'Unknown Prompt' };
          }
        })
      );

      return promptTitles.map(({ id, title }) => ({
        id,
        title,
        executions: promptStats[id].executions,
        successRate: promptStats[id].executions > 0
          ? (promptStats[id].successes / promptStats[id].executions) * 100
          : 0
      }));
    } catch (error) {
      console.warn('Failed to fetch top prompts, returning empty array:', error);
      return [];
    }
  }

  private async getCostBreakdown(userId: string, startDate: Timestamp): Promise<Array<{
    category: string;
    amount: number;
    percentage: number;
  }>> {
    try {
      const executionsQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', startDate)
      );

      const snapshot = await getDocs(executionsQuery).catch(() => ({ docs: [] }));
      const executions = snapshot.docs.map(doc => doc.data());

      const totalCost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);

      // Group costs by model
      const modelCosts: Record<string, number> = {};
      executions.forEach(exec => {
        const model = exec.model || 'unknown';
        modelCosts[model] = (modelCosts[model] || 0) + (exec.cost || 0);
      });

      return Object.entries(modelCosts).map(([category, amount]) => ({
        category,
        amount,
        percentage: totalCost > 0 ? (amount / totalCost) * 100 : 0
      })).sort((a, b) => b.amount - a.amount);
    } catch (error) {
      console.warn('Failed to fetch cost breakdown, returning empty array:', error);
      return [];
    }
  }

  // Workspace-specific methods (similar implementations)
  private async getWorkspaceMetrics(workspaceId: string, startDate: Timestamp): Promise<AnalyticsMetrics> {
    // Similar to getMetrics but filtered by workspace
    return this.getMetrics(workspaceId, startDate); // Simplified for now
  }

  private async getWorkspaceModelUsage(workspaceId: string, startDate: Timestamp): Promise<ModelUsage[]> {
    return this.getModelUsage(workspaceId, startDate); // Simplified for now
  }

  private async getWorkspaceActivityData(workspaceId: string, startDate: Timestamp, timeRange: TimeRange): Promise<ActivityData[]> {
    return this.getActivityData(workspaceId, startDate, timeRange); // Simplified for now
  }

  private async getWorkspacePerformanceMetrics(workspaceId: string, startDate: Timestamp): Promise<PerformanceMetrics> {
    return this.getPerformanceMetrics(workspaceId, startDate); // Simplified for now
  }

  private async getWorkspaceTopPrompts(workspaceId: string, startDate: Timestamp): Promise<Array<{
    id: string;
    title: string;
    executions: number;
    successRate: number;
  }>> {
    return this.getTopPrompts(workspaceId, startDate); // Simplified for now
  }

  private async getWorkspaceCostBreakdown(workspaceId: string, startDate: Timestamp): Promise<Array<{
    category: string;
    amount: number;
    percentage: number;
  }>> {
    return this.getCostBreakdown(workspaceId, startDate); // Simplified for now
  }

  // Utility methods
  private getTimeRangeMs(timeRange: TimeRange): number {
    switch (timeRange) {
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      case '30d': return 30 * 24 * 60 * 60 * 1000;
      case '90d': return 90 * 24 * 60 * 60 * 1000;
      case '1y': return 365 * 24 * 60 * 60 * 1000;
      default: return 7 * 24 * 60 * 60 * 1000;
    }
  }

  private getTimeRangeDays(timeRange: TimeRange): number {
    switch (timeRange) {
      case '24h': return 1;
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 7;
    }
  }

  // Phase 3 Advanced Analytics Methods

  /**
   * A/B Testing Framework
   */
  async createABTest(config: Omit<ABTestConfig, 'id'>): Promise<string> {
    const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const testConfig: ABTestConfig = {
      ...config,
      id: testId,
      startDate: new Date(),
      status: 'draft'
    };

    // Store test configuration
    await this.storeTestConfig(testConfig);

    return testId;
  }

  async startABTest(testId: string): Promise<void> {
    const testConfig = await this.getTestConfig(testId);
    if (!testConfig) throw new Error('Test not found');

    testConfig.status = 'running';
    testConfig.startDate = new Date();

    await this.storeTestConfig(testConfig);
  }

  async stopABTest(testId: string): Promise<void> {
    const testConfig = await this.getTestConfig(testId);
    if (!testConfig) throw new Error('Test not found');

    testConfig.status = 'completed';
    testConfig.endDate = new Date();

    await this.storeTestConfig(testConfig);
  }

  async getABTestResults(testId: string): Promise<ABTestResult[]> {
    const testConfig = await this.getTestConfig(testId);
    if (!testConfig) throw new Error('Test not found');

    const results: ABTestResult[] = [];

    for (const variant of testConfig.variants) {
      for (const metric of testConfig.metrics) {
        const result = await this.calculateVariantMetric(testId, variant.id, metric);
        results.push(result);
      }
    }

    return results;
  }

  async assignUserToVariant(testId: string, userId: string): Promise<string> {
    const testConfig = await this.getTestConfig(testId);
    if (!testConfig || testConfig.status !== 'running') {
      return testConfig?.variants[0]?.id || 'control';
    }

    // Deterministic assignment based on user ID hash
    const hash = this.hashString(userId + testId);
    const bucket = hash % 100;

    let cumulativeWeight = 0;
    for (let i = 0; i < testConfig.variants.length; i++) {
      cumulativeWeight += testConfig.trafficSplit[i];
      if (bucket < cumulativeWeight) {
        return testConfig.variants[i].id;
      }
    }

    return testConfig.variants[0].id;
  }

  /**
   * Real-time Analytics
   */
  async getRealTimeMetrics(): Promise<RealTimeMetrics> {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

    // Get recent executions for real-time metrics
    const recentExecutions = await this.getRecentExecutions(oneMinuteAgo);

    return {
      timestamp: now,
      activeUsers: await this.getActiveUserCount(oneMinuteAgo),
      requestsPerSecond: recentExecutions.length / 60,
      avgResponseTime: this.calculateAverageResponseTime(recentExecutions),
      errorRate: this.calculateErrorRate(recentExecutions),
      searchQueries: recentExecutions.filter(e => e.type === 'search').length,
      hybridSearchUsage: recentExecutions.filter(e => e.searchType === 'hybrid').length,
      semanticSearchUsage: recentExecutions.filter(e => e.searchType === 'semantic').length,
      keywordSearchUsage: recentExecutions.filter(e => e.searchType === 'keyword').length,
      cacheHitRate: this.calculateCacheHitRate(recentExecutions),
      systemLoad: await this.getSystemLoad()
    };
  }

  async subscribeToRealTimeMetrics(callback: (metrics: RealTimeMetrics) => void): Promise<() => void> {
    const interval = setInterval(async () => {
      try {
        const metrics = await this.getRealTimeMetrics();
        callback(metrics);
      } catch (error) {
        console.error('Failed to fetch real-time metrics:', error);
      }
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }

  /**
   * Cost Optimization Analytics
   */
  async getCostOptimizationMetrics(userId: string, timeRange: TimeRange = '30d'): Promise<CostOptimizationMetrics> {
    const startDate = Timestamp.fromDate(new Date(Date.now() - this.getTimeRangeMs(timeRange)));
    const executions = await this.getExecutionsInRange(userId, startDate);

    const totalCost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);
    const totalRequests = executions.length;

    return {
      totalCost,
      costPerRequest: totalRequests > 0 ? totalCost / totalRequests : 0,
      costByModel: this.calculateModelCostBreakdown(executions),
      costTrends: await this.calculateCostTrends(userId, timeRange),
      optimizationSuggestions: await this.generateOptimizationSuggestions(executions),
      budgetUtilization: await this.calculateBudgetUtilization(userId, totalCost),
      projectedMonthlyCost: this.projectMonthlyCost(executions, timeRange)
    };
  }

  /**
   * Performance Analytics with Core Web Vitals
   */
  async getAdvancedPerformanceMetrics(userId: string, timeRange: TimeRange = '7d'): Promise<PerformanceMetrics> {
    const startDate = Timestamp.fromDate(new Date(Date.now() - this.getTimeRangeMs(timeRange)));
    const executions = await this.getExecutionsInRange(userId, startDate);

    const responseTimes = executions.map(e => e.responseTime || 0).filter(t => t > 0);
    const searchLatencies = this.categorizeSearchLatencies(executions);

    return {
      responseTime: {
        avg: responseTimes.length > 0 ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
        p95: this.calculatePercentile(responseTimes, 95),
        p99: this.calculatePercentile(responseTimes, 99),
        min: Math.min(...responseTimes) || 0,
        max: Math.max(...responseTimes) || 0
      },
      errorRate: this.calculateErrorRate(executions),
      throughput: executions.length / this.getTimeRangeDays(timeRange),
      uptime: await this.calculateUptime(timeRange),
      searchLatency: searchLatencies,
      embeddingLatency: this.calculateAverageEmbeddingLatency(executions),
      cacheHitRate: this.calculateCacheHitRate(executions)
    };
  }

  // Helper methods for Phase 3 analytics

  private async storeTestConfig(config: ABTestConfig): Promise<void> {
    // Store in localStorage for now, in production use Firestore
    localStorage.setItem(`ab_test_${config.id}`, JSON.stringify(config));
  }

  private async getTestConfig(testId: string): Promise<ABTestConfig | null> {
    const stored = localStorage.getItem(`ab_test_${testId}`);
    return stored ? JSON.parse(stored) : null;
  }

  private async calculateVariantMetric(testId: string, variantId: string, metric: string): Promise<ABTestResult> {
    // Simplified implementation - in production, query actual user data
    const sampleSize = Math.floor(Math.random() * 1000) + 100;
    const value = Math.random() * 100;

    return {
      testId,
      variantId,
      metric,
      value,
      sampleSize,
      conversionRate: metric === 'conversion' ? value : undefined,
      confidenceInterval: [value * 0.9, value * 1.1],
      statisticalSignificance: Math.random() > 0.5 ? 0.95 : 0.85
    };
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private async getRecentExecutions(_since: Date): Promise<any[]> {
    // Simplified implementation
    return [];
  }

  private async getActiveUserCount(_since: Date): Promise<number> {
    // Simplified implementation
    return Math.floor(Math.random() * 50) + 10;
  }

  private calculateAverageResponseTime(executions: any[]): number {
    if (executions.length === 0) return 0;
    const times = executions.map(e => e.responseTime || 0);
    return times.reduce((a, b) => a + b, 0) / times.length;
  }

  private calculateErrorRate(executions: any[]): number {
    if (executions.length === 0) return 0;
    const errors = executions.filter(e => e.status === 'error').length;
    return (errors / executions.length) * 100;
  }

  private calculateCacheHitRate(executions: any[]): number {
    if (executions.length === 0) return 0;
    const cacheHits = executions.filter(e => e.cacheHit === true).length;
    return (cacheHits / executions.length) * 100;
  }

  private async getSystemLoad(): Promise<{ cpu: number; memory: number; storage: number }> {
    // Simplified implementation - in production, get from monitoring service
    return {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      storage: Math.random() * 100
    };
  }

  private async getExecutionsInRange(userId: string, startDate: Timestamp): Promise<any[]> {
    const executionsQuery = query(
      this.executionsCollection,
      where('userId', '==', userId),
      where('createdAt', '>=', startDate)
    );

    const snapshot = await getDocs(executionsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  private calculateModelCostBreakdown(executions: any[]): ModelCostBreakdown[] {
    const modelStats: Record<string, { requests: number; totalCost: number }> = {};

    executions.forEach(exec => {
      const model = exec.model || 'unknown';
      if (!modelStats[model]) {
        modelStats[model] = { requests: 0, totalCost: 0 };
      }
      modelStats[model].requests++;
      modelStats[model].totalCost += exec.cost || 0;
    });

    const totalCost = Object.values(modelStats).reduce((sum, stats) => sum + stats.totalCost, 0);

    return Object.entries(modelStats).map(([modelName, stats]) => ({
      modelName,
      requests: stats.requests,
      totalCost: stats.totalCost,
      avgCostPerRequest: stats.requests > 0 ? stats.totalCost / stats.requests : 0,
      percentage: totalCost > 0 ? (stats.totalCost / totalCost) * 100 : 0
    }));
  }

  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  private categorizeSearchLatencies(executions: any[]): { semantic: number; keyword: number; hybrid: number } {
    const semanticLatencies = executions
      .filter(e => e.searchType === 'semantic')
      .map(e => e.responseTime || 0);
    const keywordLatencies = executions
      .filter(e => e.searchType === 'keyword')
      .map(e => e.responseTime || 0);
    const hybridLatencies = executions
      .filter(e => e.searchType === 'hybrid')
      .map(e => e.responseTime || 0);

    return {
      semantic: semanticLatencies.length > 0 ? semanticLatencies.reduce((a, b) => a + b, 0) / semanticLatencies.length : 0,
      keyword: keywordLatencies.length > 0 ? keywordLatencies.reduce((a, b) => a + b, 0) / keywordLatencies.length : 0,
      hybrid: hybridLatencies.length > 0 ? hybridLatencies.reduce((a, b) => a + b, 0) / hybridLatencies.length : 0
    };
  }

  private calculateAverageEmbeddingLatency(executions: any[]): number {
    const embeddingTimes = executions
      .map(e => e.embeddingTime || 0)
      .filter(t => t > 0);
    return embeddingTimes.length > 0 ? embeddingTimes.reduce((a, b) => a + b, 0) / embeddingTimes.length : 0;
  }

  private async calculateUptime(_timeRange: TimeRange): Promise<number> {
    // Simplified implementation - in production, calculate from health checks
    return 99.95; // 99.95% uptime
  }



  private async calculateCostTrends(userId: string, timeRange: TimeRange): Promise<CostTrendData[]> {
    const days = this.getTimeRangeDays(timeRange);
    const trends: CostTrendData[] = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayQuery = query(
        this.executionsCollection,
        where('userId', '==', userId),
        where('createdAt', '>=', Timestamp.fromDate(dayStart)),
        where('createdAt', '<', Timestamp.fromDate(dayEnd))
      );

      try {
        const snapshot = await getDocs(dayQuery);
        const executions = snapshot.docs.map(doc => doc.data());
        const cost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);
        const requests = executions.length;

        trends.push({
          date: dayStart.toISOString().split('T')[0],
          cost,
          requests,
          efficiency: requests > 0 ? cost / requests : 0
        });
      } catch (error) {
        trends.push({
          date: dayStart.toISOString().split('T')[0],
          cost: 0,
          requests: 0,
          efficiency: 0
        });
      }
    }

    return trends;
  }

  private async generateOptimizationSuggestions(executions: any[]): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];

    // Analyze model usage for potential switches
    const modelCosts = this.calculateModelCostBreakdown(executions);
    const highCostModels = modelCosts.filter(m => m.avgCostPerRequest > 0.01);

    if (highCostModels.length > 0) {
      suggestions.push({
        type: 'model_switch',
        title: 'Consider switching to more cost-effective models',
        description: `High-cost models detected. Consider switching to alternatives for non-critical tasks.`,
        potentialSavings: highCostModels.reduce((sum, m) => sum + m.totalCost * 0.3, 0),
        effort: 'medium',
        impact: 'high'
      });
    }

    // Check for caching opportunities
    const duplicateQueries = executions.filter((exec, index, arr) =>
      arr.findIndex(e => e.query === exec.query) !== index
    );

    if (duplicateQueries.length > 10) {
      suggestions.push({
        type: 'caching',
        title: 'Implement query caching',
        description: `${duplicateQueries.length} duplicate queries detected. Caching could reduce costs.`,
        potentialSavings: duplicateQueries.reduce((sum, exec) => sum + (exec.cost || 0), 0) * 0.8,
        effort: 'low',
        impact: 'medium'
      });
    }

    return suggestions;
  }

  private async calculateBudgetUtilization(_userId: string, currentCost: number): Promise<number> {
    // In a real implementation, this would fetch the user's budget from the database
    // For now, assume a default monthly budget of $100
    const monthlyBudget = 100;
    return Math.min((currentCost / monthlyBudget) * 100, 100);
  }

  private projectMonthlyCost(executions: any[], timeRange: TimeRange): number {
    if (executions.length === 0) return 0;

    const totalCost = executions.reduce((sum, exec) => sum + (exec.cost || 0), 0);
    const days = this.getTimeRangeDays(timeRange);
    const dailyAverage = totalCost / days;

    return dailyAverage * 30; // Project to 30 days
  }
}

export const analyticsService = new AnalyticsService();
