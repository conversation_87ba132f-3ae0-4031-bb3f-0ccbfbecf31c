/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import viteCompression from 'vite-plugin-compression'
import { visualizer } from 'rollup-plugin-visualizer'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Gzip compression
    viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024, // Only compress files larger than 1KB
      compressionOptions: {
        level: 9, // Maximum compression
      },
      filter: /\.(js|mjs|json|css|html|svg)$/i,
      deleteOriginFile: false,
    }),
    // Brotli compression (better than gzip)
    viteCompression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024,
      compressionOptions: {
        level: 11, // Maximum compression
      },
      filter: /\.(js|mjs|json|css|html|svg)$/i,
      deleteOriginFile: false,
    }),
    // Bundle analyzer (only in analyze mode)
    ...(process.env.ANALYZE ? [
      visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap', // or 'sunburst', 'network'
      })
    ] : []),
  ],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/test/setup.ts',
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
        'coverage/',
      ],
    },
  },
  resolve: {
    alias: {
      // Ensure React ecosystem is resolved consistently
      'react': 'react',
      'react-dom': 'react-dom',
      'react-router-dom': 'react-router-dom',
      // Fix react-is compatibility with Recharts
      'react-is': path.resolve(__dirname, 'src/utils/react-is-compat.js'),
      // Comprehensive es-toolkit compatibility fix - map all functions to universal compat
      'es-toolkit/compat/get': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/uniqBy': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/sortBy': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/isEqual': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/last': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/isPlainObject': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/maxBy': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/minBy': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/range': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/throttle': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/omit': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/sumBy': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/isNil': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat/isFunction': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      'es-toolkit/compat': path.resolve(__dirname, 'src/utils/es-toolkit-universal-compat.js'),
      // Fix use-sync-external-store compatibility
      'use-sync-external-store/shim/with-selector': path.resolve(__dirname, 'src/utils/use-sync-external-store-compat.js'),
    },
    dedupe: ['react', 'react-dom', 'react-router-dom', '@tanstack/react-query', 'react-window'],
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: process.env.NODE_ENV === 'development',
    minify: 'terser',
    target: 'es2020',
    modulePreload: false,
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true,
    },
    // CDN Configuration
    ...(process.env.VITE_CDN_URL && {
      base: process.env.VITE_CDN_URL,
    }),
    // Enhanced build optimization
    cssCodeSplit: true,
    assetsInlineLimit: 4096, // 4KB - inline smaller assets
    chunkSizeWarningLimit: 500, // Warn for chunks > 500KB
    reportCompressedSize: false, // Faster builds
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true,
        pure_funcs: process.env.NODE_ENV === 'production' ? ['console.log', 'console.info'] : [],
      },
      mangle: {
        safari10: true,
      },
    },
    rollupOptions: {
      output: {
        format: 'es',
        entryFileNames: 'assets/js/[name]-[hash].js',
        chunkFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `assets/fonts/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // Core React ecosystem - include ALL React-dependent libraries to avoid module resolution issues
            if (id.includes('react') ||
                id.includes('@tanstack/react-query') ||
                id.includes('@headlessui/react') ||
                id.includes('@heroicons/react') ||
                id.includes('lucide-react')) {
              return 'vendor-react-core';
            }

            // Firebase - consolidated to avoid module dependency issues
            // All Firebase modules must be in the same chunk to prevent temporal dead zone errors
            if (id.includes('firebase') || id.includes('@firebase')) {
              return 'vendor-firebase';
            }

            // UI libraries that don't depend on React
            // (React-dependent UI libraries are now in vendor-react-core)

            // Charts and visualization - heavy libraries
            if (id.includes('recharts')) {
              return 'vendor-recharts';
            }
            if (id.includes('d3-')) {
              return 'vendor-d3';
            }



            // Utilities - split by type
            if (id.includes('date-fns')) {
              return 'vendor-date-utils';
            }
            if (id.includes('lodash') || id.includes('es-toolkit')) {
              return 'vendor-utils';
            }

            // Tailwind and CSS
            if (id.includes('tailwind') || id.includes('@tailwindcss')) {
              return 'vendor-tailwind';
            }

            // Everything else
            return 'vendor-misc';
          }

          // Application code splitting
          if (id.includes('/pages/')) {
            const pageName = id.split('/pages/')[1].split('.')[0];
            return `page-${pageName.toLowerCase()}`;
          }

          if (id.includes('/components/')) {
            // Group heavy components
            if (id.includes('components/charts') || id.includes('components/analytics')) {
              return 'components-charts';
            }
            if (id.includes('components/editor') || id.includes('components/monaco')) {
              return 'components-editor';
            }
            return 'components-common';
          }
        },
      },
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-dom/client',
      'react/jsx-runtime',
      'react/jsx-dev-runtime',
      'react-router-dom',
      'react-window',
      'react-window-infinite-loader',
      '@tanstack/react-query',
      '@tanstack/react-query-devtools',
      '@headlessui/react',
      '@heroicons/react',
      'lucide-react',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'eventemitter3',
      'recharts'
    ],
    exclude: [
      // Exclude large libraries that should be loaded on demand
      '@heroicons/react/24/outline',
      '@heroicons/react/24/solid',
      // Exclude all es-toolkit modules to prevent module resolution issues
      'es-toolkit',
      'es-toolkit/compat',
      'es-toolkit/compat/get',
      'es-toolkit/compat/uniqBy'
    ]
  },
  // Enable tree shaking for better optimization
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
  },
  // Enhanced tree shaking configuration
  esbuild: {
    treeShaking: true,
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
  },
})
