import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import {
  analyticsService,
  UsageAnalytics,
  TimeRange,
  RealTimeMetrics,
  ABTestConfig,
  ABTestResult,
  CostOptimizationMetrics,
  PerformanceMetrics
} from '../services/analyticsService';
import {
  ActivityChart,
  ModelUsageChart,
  CostBreakdownChart,
  PerformanceChart,
  MetricCard,
  TopPromptsTable
} from '../components/analytics/Charts';
import {
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  PlayIcon,
  ArrowTrendingUpIcon,
  UserGroupIcon,
  CpuChipIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

// Mock data generator for development
const generateMockAnalyticsData = (): UsageAnalytics => {
  const now = new Date();
  const daysBack = 7;

  // Generate activity data for the last 7 days
  const activityData = Array.from({ length: daysBack }, (_, i) => {
    const date = new Date(now);
    date.setDate(date.getDate() - (daysBack - 1 - i));
    return {
      date: date.toISOString().split('T')[0],
      prompts: Math.floor(Math.random() * 20) + 5,
      executions: Math.floor(Math.random() * 50) + 10,
      documents: Math.floor(Math.random() * 15) + 2,
      cost: Math.random() * 5 + 1
    };
  });

  const totalExecutions = activityData.reduce((sum, day) => sum + day.executions, 0);
  const totalCost = activityData.reduce((sum, day) => sum + day.cost, 0);

  return {
    metrics: {
      totalPrompts: Math.floor(Math.random() * 100) + 25,
      totalExecutions,
      totalDocuments: Math.floor(Math.random() * 50) + 15,
      avgExecutionTime: Math.random() * 2000 + 500,
      successRate: Math.random() * 20 + 80,
      totalCost,
      avgCostPerExecution: totalCost / totalExecutions
    },
    modelUsage: [
      { name: 'GPT-4', usage: Math.floor(Math.random() * 100) + 50, cost: Math.random() * 20 + 10, avgResponseTime: Math.random() * 1000 + 800 },
      { name: 'GPT-3.5-turbo', usage: Math.floor(Math.random() * 80) + 30, cost: Math.random() * 10 + 5, avgResponseTime: Math.random() * 800 + 400 },
      { name: 'Claude-3', usage: Math.floor(Math.random() * 60) + 20, cost: Math.random() * 15 + 8, avgResponseTime: Math.random() * 1200 + 600 }
    ],
    activityData,
    performanceMetrics: {
      responseTime: {
        avg: Math.random() * 1000 + 500,
        p95: Math.random() * 2000 + 1000,
        p99: Math.random() * 3000 + 2000
      },
      errorRate: Math.random() * 5 + 1,
      throughput: Math.random() * 100 + 50,
      uptime: Math.random() * 2 + 98
    },
    topPrompts: [
      { id: '1', title: 'Code Review Assistant', executions: Math.floor(Math.random() * 50) + 20, successRate: Math.random() * 20 + 80 },
      { id: '2', title: 'Document Summarizer', executions: Math.floor(Math.random() * 40) + 15, successRate: Math.random() * 20 + 75 },
      { id: '3', title: 'Email Generator', executions: Math.floor(Math.random() * 30) + 10, successRate: Math.random() * 20 + 85 },
      { id: '4', title: 'Data Analyzer', executions: Math.floor(Math.random() * 25) + 8, successRate: Math.random() * 20 + 70 },
      { id: '5', title: 'Content Creator', executions: Math.floor(Math.random() * 20) + 5, successRate: Math.random() * 20 + 90 }
    ],
    costBreakdown: [
      { category: 'GPT-4', amount: Math.random() * 20 + 10, percentage: 45 },
      { category: 'GPT-3.5-turbo', amount: Math.random() * 10 + 5, percentage: 30 },
      { category: 'Claude-3', amount: Math.random() * 15 + 8, percentage: 25 }
    ]
  };
};

export const Analytics: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>('7d');
  const [analyticsData, setAnalyticsData] = useState<UsageAnalytics | null>(null);

  // Phase 3 Advanced Analytics State
  const [activeTab, setActiveTab] = useState<'overview' | 'realtime' | 'abtests' | 'optimization'>('overview');
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [abTests, setAbTests] = useState<ABTestConfig[]>([]);
  const [abTestResults, setAbTestResults] = useState<Record<string, ABTestResult[]>>({});
  const [costOptimization, setCostOptimization] = useState<CostOptimizationMetrics | null>(null);
  const [advancedPerformance, setAdvancedPerformance] = useState<PerformanceMetrics | null>(null);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user) {
        console.log('Analytics: No user found');
        return;
      }

      console.log('Analytics: Fetching data for user:', user.uid, 'timeRange:', timeRange);
      setLoading(true);
      setError(null);

      try {
        const data = await analyticsService.getUserAnalytics(user.uid, timeRange);
        console.log('Analytics: Data fetched successfully:', data);
        setAnalyticsData(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch analytics';
        console.error('Analytics: Error fetching data:', err);
        setError(errorMessage);

        // For development: Set realistic mock data if analytics fetch fails
        console.log('Analytics: Setting mock data for development');
        const mockData = generateMockAnalyticsData();
        setAnalyticsData(mockData);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [user, timeRange]);

  // Phase 3: Real-time metrics subscription
  useEffect(() => {
    if (!realTimeEnabled || !user) return;

    let unsubscribe: (() => void) | undefined;

    const startRealTimeMetrics = async () => {
      try {
        unsubscribe = await analyticsService.subscribeToRealTimeMetrics((metrics) => {
          setRealTimeMetrics(metrics);
        });
      } catch (error) {
        console.error('Failed to start real-time metrics:', error);
      }
    };

    startRealTimeMetrics();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [realTimeEnabled, user]);

  // Phase 3: Load advanced analytics data
  useEffect(() => {
    const loadAdvancedAnalytics = async () => {
      if (!user) return;

      try {
        // Load cost optimization metrics
        const costData = await analyticsService.getCostOptimizationMetrics(user.uid, timeRange);
        setCostOptimization(costData);

        // Load advanced performance metrics
        const perfData = await analyticsService.getAdvancedPerformanceMetrics(user.uid, timeRange);
        setAdvancedPerformance(perfData);

        // Load A/B tests (simplified - in production, load from backend)
        const mockABTests: ABTestConfig[] = [
          {
            id: 'test_1',
            name: 'Hybrid Search vs Semantic Search',
            description: 'Compare hybrid search performance against semantic-only search',
            variants: [
              { id: 'control', name: 'Semantic Only', description: 'Traditional semantic search', config: { searchType: 'semantic' }, weight: 50 },
              { id: 'treatment', name: 'Hybrid Search', description: 'BM25 + Semantic fusion', config: { searchType: 'hybrid' }, weight: 50 }
            ],
            trafficSplit: [50, 50],
            startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            status: 'running',
            metrics: ['response_time', 'relevance_score', 'user_satisfaction'],
            targetMetric: 'relevance_score',
            minimumSampleSize: 1000,
            confidenceLevel: 0.95
          }
        ];
        setAbTests(mockABTests);

        // Load A/B test results
        const testResults: Record<string, ABTestResult[]> = {};
        for (const test of mockABTests) {
          testResults[test.id] = await analyticsService.getABTestResults(test.id);
        }
        setAbTestResults(testResults);

      } catch (error) {
        console.error('Failed to load advanced analytics:', error);
      }
    };

    if (activeTab !== 'overview') {
      loadAdvancedAnalytics();
    }
  }, [user, timeRange, activeTab]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error && !analyticsData) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-yellow-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Analytics Unavailable</h3>
              <p className="mt-1 text-sm text-gray-500">
                Unable to load analytics data at this time.
              </p>

              {/* Development-specific messaging */}
              {import.meta.env.DEV && (
                <div className="mt-4 p-3 bg-blue-50 rounded-md">
                  <p className="text-xs text-blue-700">
                    <strong>Development Mode:</strong> This is expected when the database is empty.
                    Analytics will populate as you use the application.
                  </p>
                </div>
              )}

              {/* Error details for debugging */}
              {import.meta.env.DEV && error && (
                <details className="mt-3 text-left">
                  <summary className="text-xs text-gray-500 cursor-pointer">
                    Technical Details
                  </summary>
                  <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
                    {error}
                  </pre>
                </details>
              )}

              <div className="mt-6 flex flex-col sm:flex-row gap-3">
                <button
                  onClick={() => window.location.reload()}
                  className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                >
                  Retry
                </button>
                <button
                  onClick={() => window.history.back()}
                  className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data</h3>
        <p className="mt-1 text-sm text-gray-500">
          Start using prompts to see your analytics data.
        </p>
      </div>
    );
  }

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ComponentType<any>;
    trend?: number;
    suffix?: string;
  }> = ({ title, value, icon: Icon, trend, suffix = '' }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {value}{suffix}
                </div>
                {trend !== undefined && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                    trend >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <ArrowTrendingUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                    {Math.abs(trend)}%
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Development Mode Banner */}
      {import.meta.env.DEV && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <DocumentTextIcon className="h-5 w-5 text-blue-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Development Mode
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  You're viewing mock analytics data. Real data will appear as you use the application.
                  {error && (
                    <>
                      <br />
                      <span className="text-xs">Database connection: {error}</span>
                    </>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Production Error Banner */}
      {!import.meta.env.DEV && error && analyticsData && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Analytics data unavailable
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Unable to load analytics from database. Please try refreshing the page.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Analytics Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Track your prompt usage, performance, and costs
          </p>
        </div>
        <div className="mt-4 flex space-x-4 md:mt-0 md:ml-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as TimeRange)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>

          {/* Phase 3: Real-time toggle */}
          <button
            onClick={() => setRealTimeEnabled(!realTimeEnabled)}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              realTimeEnabled
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-gray-100 text-gray-700 border border-gray-200'
            }`}
          >
            {realTimeEnabled ? '🟢 Live' : '⚪ Static'}
          </button>
        </div>
      </div>

      {/* Phase 3: Advanced Analytics Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: ChartBarIcon },
            { id: 'realtime', name: 'Real-time', icon: ClockIcon },
            { id: 'abtests', name: 'A/B Tests', icon: UserGroupIcon },
            { id: 'optimization', name: 'Cost Optimization', icon: CurrencyDollarIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Prompts"
          value={analyticsData.metrics.totalPrompts}
          icon={DocumentTextIcon}
          color="blue"
        />
        <MetricCard
          title="Total Executions"
          value={analyticsData.metrics.totalExecutions}
          icon={PlayIcon}
          color="green"
        />
        <MetricCard
          title="Success Rate"
          value={`${analyticsData.metrics.successRate.toFixed(1)}%`}
          icon={ArrowTrendingUpIcon}
          color="green"
        />
        <MetricCard
          title="Total Cost"
          value={`$${analyticsData.metrics.totalCost.toFixed(2)}`}
          icon={CurrencyDollarIcon}
          color="yellow"
        />
      </div>

      {/* Charts and detailed analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Model Usage */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Model Usage</h3>
          <div className="space-y-3">
            {analyticsData.topModels.map((model, index) => (
              <div key={model.name} className="flex items-center">
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{model.name}</span>
                    <span className="text-sm text-gray-500">{model.usage}%</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-indigo-600 h-2 rounded-full"
                      style={{ width: `${model.usage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cost Analysis */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Analysis</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Total Cost</span>
              <span className="text-lg font-semibold text-gray-900">
                ${analyticsData.costAnalysis.totalCost.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Avg Cost per Execution</span>
              <span className="text-sm font-medium text-gray-900">
                ${analyticsData.costAnalysis.avgCostPerExecution.toFixed(3)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Monthly Trend</span>
              <span className={`text-sm font-medium ${
                analyticsData.costAnalysis.monthlyTrend >= 0 ? 'text-red-600' : 'text-green-600'
              }`}>
                {analyticsData.costAnalysis.monthlyTrend >= 0 ? '+' : ''}
                {analyticsData.costAnalysis.monthlyTrend.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Phase 3: Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Existing overview content is already rendered above */}
        </div>
      )}

      {activeTab === 'realtime' && (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Real-time Metrics</h3>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${realTimeEnabled ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                <span className="text-sm text-gray-500">
                  {realTimeEnabled ? 'Live Updates' : 'Static Data'}
                </span>
              </div>
            </div>

            {realTimeMetrics ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Active Users</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.activeUsers}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Requests/sec</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.requestsPerSecond.toFixed(1)}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Avg Response Time</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.avgResponseTime.toFixed(0)}ms</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Error Rate</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.errorRate.toFixed(1)}%</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Hybrid Search Usage</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.hybridSearchUsage}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Cache Hit Rate</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.cacheHitRate.toFixed(1)}%</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">CPU Usage</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.systemLoad.cpu.toFixed(1)}%</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Memory Usage</div>
                  <div className="text-2xl font-bold text-gray-900">{realTimeMetrics.systemLoad.memory.toFixed(1)}%</div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Enable Real-time Monitoring</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Turn on live updates to see real-time metrics
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'abtests' && (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">A/B Tests</h3>
              <button className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm hover:bg-indigo-700">
                Create New Test
              </button>
            </div>

            {abTests.length > 0 ? (
              <div className="space-y-4">
                {abTests.map((test) => (
                  <div key={test.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-md font-medium text-gray-900">{test.name}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        test.status === 'running' ? 'bg-green-100 text-green-800' :
                        test.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {test.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{test.description}</p>

                    <div className="grid grid-cols-2 gap-4">
                      {test.variants.map((variant, index) => (
                        <div key={variant.id} className="bg-gray-50 p-3 rounded">
                          <div className="text-sm font-medium text-gray-900">{variant.name}</div>
                          <div className="text-xs text-gray-500">{variant.description}</div>
                          <div className="text-xs text-gray-500 mt-1">Traffic: {test.trafficSplit[index]}%</div>
                        </div>
                      ))}
                    </div>

                    {abTestResults[test.id] && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="text-sm font-medium text-gray-900 mb-2">Results</div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          {abTestResults[test.id].map((result) => (
                            <div key={`${result.variantId}-${result.metric}`}>
                              <div className="text-gray-500">{result.metric}</div>
                              <div className="font-medium">{result.value.toFixed(2)}</div>
                              <div className="text-xs text-gray-400">n={result.sampleSize}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No A/B Tests</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Create your first A/B test to optimize performance
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'optimization' && (
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Optimization</h3>

            {costOptimization ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">Total Cost</div>
                    <div className="text-2xl font-bold text-gray-900">${costOptimization.totalCost.toFixed(2)}</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">Cost per Request</div>
                    <div className="text-2xl font-bold text-gray-900">${costOptimization.costPerRequest.toFixed(4)}</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm text-gray-500">Projected Monthly</div>
                    <div className="text-2xl font-bold text-gray-900">${costOptimization.projectedMonthlyCost.toFixed(2)}</div>
                  </div>
                </div>

                {costOptimization.optimizationSuggestions.length > 0 && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">Optimization Suggestions</h4>
                    <div className="space-y-3">
                      {costOptimization.optimizationSuggestions.map((suggestion, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="text-sm font-medium text-gray-900">{suggestion.title}</h5>
                            <span className="text-sm text-green-600 font-medium">
                              Save ${suggestion.potentialSavings.toFixed(2)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                          <div className="flex space-x-4 text-xs">
                            <span className={`px-2 py-1 rounded ${
                              suggestion.effort === 'low' ? 'bg-green-100 text-green-800' :
                              suggestion.effort === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {suggestion.effort} effort
                            </span>
                            <span className={`px-2 py-1 rounded ${
                              suggestion.impact === 'high' ? 'bg-green-100 text-green-800' :
                              suggestion.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {suggestion.impact} impact
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Loading Cost Analysis</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Analyzing your usage patterns for optimization opportunities
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
